import os
import subprocess
import spotipy
from spotipy.oauth2 import SpotifyOAuth

# ---- Config ----
SPOTIPY_CLIENT_ID = '57f12a98a5aa492cb8ce0e29d6065555'
SPOTIPY_CLIENT_SECRET = '97b45bb40d3f4cd393e4c55abe3cba5f'
SPOTIPY_REDIRECT_URI = 'http://127.0.0.1:8888/callback'
DEEMIX_PATH = 'C:/Users/<USER>/Downloads/windows deemix gui/win-x64_portable-latest.exe'  # or wherever deemix is installed
DEEMIX_DOWNLOAD_DIR = 'C:/Users/<USER>/Downloads/DeemixTest'

scope = "user-library-read playlist-read-private playlist-read-collaborative"
sp = spotipy.Spotify(auth_manager=SpotifyOAuth(
    client_id=SPOTIPY_CLIENT_ID,
    client_secret=SPOTIPY_CLIENT_SECRET,
    redirect_uri=SPOTIPY_REDIRECT_URI,
    scope=scope
))

def deemix_search_and_download(query):
    print(f"Searching and downloading: {query}")
    try:
        subprocess.run([
            DEEMIX_PATH,
            '-b',  # best quality
            '-p', DEEMIX_DOWNLOAD_DIR,
            query
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Failed to download '{query}': {e}")

# ---- Spotify Helpers ----
def get_liked_tracks():
    liked = []
    results = sp.current_user_saved_tracks(limit=50)
    print("Fetching Liked Songs...")
    while results:
        for item in results['items']:
            track = item['track']
            track_str = f"{track['name']} {track['artists'][0]['name']}"
            print(f"  Liked: {track_str}")
            liked.append(track_str)
        if results['next']:
            results = sp.next(results)
        else:
            break
    return liked

def get_playlist_tracks():
    tracks = []
    playlists = sp.current_user_playlists()
    print("Fetching Playlists...")
    for playlist in playlists['items']:
        print(f"  Playlist: {playlist['name']}")
        results = sp.playlist_tracks(playlist['id'])
        for item in results['items']:
            track = item['track']
            if track:
                track_str = f"{track['name']} {track['artists'][0]['name']}"
                print(f"    Track: {track_str}")
                tracks.append(track_str)
    return tracks

#def get_discovery_weekly():
#    results = sp.current_user_playlists()
#    print("Looking for Discover Weekly...")
#    for playlist in results['items']:
#        if "Discover Weekly" in playlist['name']:
#            print("  Found Discover Weekly.")
#            discovery_tracks = []
#            tracks = sp.playlist_tracks(playlist['id'])
#            for item in tracks['items']:
#                track = item['track']
#                if track:
#                    track_str = f"{track['name']} {track['artists'][0]['name']}"
#                    print(f"    Discover Weekly: {track_str}")
#                    discovery_tracks.append(track_str)
#            return discovery_tracks
#    print("  Discover Weekly not found.")
#    return []

# ---- Main ----
if __name__ == "__main__":
    os.makedirs(DEEMIX_DOWNLOAD_DIR, exist_ok=True)
    print("Gathering songs from Spotify...\n")
    all_tracks = set()
    all_tracks.update(get_liked_tracks())
    all_tracks.update(get_playlist_tracks())
    #all_tracks.update(get_discovery_weekly())

    print(f"\nFound {len(all_tracks)} unique songs. Starting download...\n")
    for track_query in all_tracks:
        deemix_search_and_download(track_query)
