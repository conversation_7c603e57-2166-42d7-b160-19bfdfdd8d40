mqtt:
  enabled: true
  host: ************
  user: mqtt-user
  password: System64!

#ui:
#  live_mode: webrtc

database:
  path: /config/frigate.db

detectors:
  coral:
    type: edgetpu
    device: usb

ffmpeg:
  #hwaccel_args: preset-nvidia-h264
  hwaccel_args: preset-nvidia
  output_args:
    record: preset-record-generic-audio-aac
    #record: preset-record-generic-audio-copy

model:
  path: plus://8205bcbe8038ce6dfb0d598d6d3fa62e

go2rtc:
  streams:
    EianCam:
      - rtsp://eiancam:eiancam@*************:554/stream1
      #- tapo://fTORP98QNpb$SD3w@*************
    OwenCam:
      - rtsp://owencam:owencam@*************:554/stream1
    KitchenCam:
      - rtsp://kitchencam:kitchencam@*************:554/stream1
    GarageCam:
      - rtsp://garagecam:garagecam@*************:554/stream1
    DoorbellCam:
      - rtsp://admin:Doorbell32!@************:554/h265Preview_01_main
    BackPorchCam:
      - rtsp://admin:BackPorch32!@*************:554/h265Preview_01_main
    DrivewayCam:
      - rtsp://admin:Driveway32!@192.168.1.243:554/h265Preview_01_main
    BackyardCam:
      - rtsp://admin:Backyard32!@192.168.1.109:554/h265Preview_01_main
    SumpPumpCam:
      - rtsp://sumppumpcam:sumppumpcam@*************:554/stream1
    BasementCam:
      - rtsp://basementcam:basementcam@*************:554/stream1
    ShopCam:
      - rtsp://shopcam:shopcam@*************:554/stream1
    UtilityCam:
      - rtsp://utilitycam:utilitycam@*************:554/stream1

cameras:
  EianCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/EianCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: EianCam
    detect:
      enabled: true
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    review:
      detections:
        labels:
          - person
    #      - cat
    #      - dog
    objects:
      track:
        - person
      mask: 0.136,0.166,0.211,0.93,0.444,0.802,0.45,0.571,0.36,0.162
  OwenCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/OwenCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: OwenCam
    detect:
      enabled: true
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    review:
      detections:
        labels:
          - person
    #      - cat
    #      - dog
    objects:
      track:
        - person
      mask: 0.662,0.191,0.544,0.677,0.591,0.854,0.812,0.881,0.837,0.237
  KitchenCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/KitchenCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: KitchenCam
    detect:
      enabled: true
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    #review:
    #  detections:
    #    labels:
    #      - person
    #      - cat
    #      - dog
  GarageCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/GarageCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: GarageCam
    detect:
      enabled: false
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    #review:
    #  detections:
    #    labels:
    #      - person
    #      - cat
    #      - dog
  DoorbellCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/DoorbellCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: DoorbellCam
    detect:
      enabled: true
      width: 2560
      height: 1920
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    review:
      detections:
        labels:
          - person
          - amazon
          - fedex
          - ups
          - package
    objects:
      track:
        - person
        #- cat
        #- dog
        - amazon
        - fedex
        - ups
        - package
      mask: 0.148,0.099,0.366,0.148,0.371,0.471,0,0.565,0,0.349
  BackPorchCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/BackPorchCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
            - audio
    live:
      stream_name: BackPorchCam
    detect:
      enabled: true
      width: 3840
      height: 2160
      fps: 10
    objects:
      track:
        - person
        #- cat
        - dog
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    audio:
      enabled: true
      listen:
        - bark
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    review:
      detections:
        labels:
          #- person
          #- cat
          - dog
    zones:
      DogZone:
        coordinates: 0.171,0.25,0.221,0.858,0.597,0.84,0.529,0.273
        loitering_time: 10
        objects: dog
  BackyardCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/BackyardCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: BackyardCam
    detect:
      enabled: true
      width: 3840
      height: 2160
      fps: 10
    objects:
      track:
        #- person
        - cat
        - dog
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    review:
      detections:
        labels:
          - person
          - cat
          - dog
  DrivewayCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/DrivewayCam
          input_args: preset-rtsp-restream
          roles:
            - record
            - detect
    live:
      stream_name: DrivewayCam
    detect:
      enabled: false
      width: 4608
      height: 1728
      fps: 10
    objects:
      track:
        - person
        - cat
        - dog
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    review:
      detections:
        labels:
          - person
          - cat
          - dog
    # objects:
    #   track:
    #     - person
    #     - cat
    #     - amazon
    #     - fedex
    #     - ups
    #     - package
  BasementCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/BasementCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: BasementCam
    detect:
      enabled: false
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    #review:
    #  detections:
    #    labels:
    #      - person
    #      - cat
    #      - dog
  ShopCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/ShopCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: ShopCam
    detect:
      enabled: false
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
   #review:
   #  detections:
   #    labels:
   #      - person
   #      - cat
   #      - dog
  SumpPumpCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/SumpPumpCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: SumpPumpCam
    detect:
      enabled: false
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    #review:
    #  detections:
    #    labels:
    #      - person
    #      - cat
    #      - dog
  UtilityCam:
    ffmpeg:
      inputs:
        - path: rtsp://127.0.0.1:8554/UtilityCam
          input_args: preset-rtsp-restream
          roles:
            - detect
            - clips
            - record
    live:
      stream_name: UtilityCam
    detect:
      enabled: false
      width: 1920
      height: 1080
      fps: 10
    record:
      enabled: true
      retain:
        days: 7
        mode: all
      alerts:
        retain:
          days: 14
      detections:
        retain:
          days: 14
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    #review:
    #  detections:
    #    labels:
    #      - person
    #      - cat
    #      - dog
objects:
  track:
    - person
    - cat
    - dog
    - car
    #- bird
    #- airplane
    - bicycle
    #- motorcycle
    - bus
    - license_plate
    - amazon
    - fedex
    - ups
    - package

snapshots:
  # Optional: Enable writing jpg snapshot to /media/frigate/clips (default: shown below)
  enabled: true
  # Optional: save a clean PNG copy of the snapshot image (default: shown below)
  clean_copy: true
  # Optional: print a timestamp on the snapshots (default: shown below)
  timestamp: false
  # Optional: draw bounding box on the snapshots (default: shown below)
  bounding_box: true
  # Optional: crop the snapshot (default: shown below)
  crop: false
  # Optional: height to resize the snapshot to (default: original size)
  #height: 175
  # Optional: Restrict snapshots to objects that entered any of the listed zones (default: no required zones)
  #required_zones: []
  # Optional: Camera override for retention settings (default: global values)
  retain:
    # Required: Default retention days (default: shown below)
    default: 1
    objects:
      person: 1
      car: 1
      package: 1
      fedex: 1
      ups: 1
      amazon: 1

version: 0.15-1
camera_groups:
  Kids:
    order: 1
    icon: LuPersonStanding
    cameras:
      - EianCam
      - OwenCam
  House:
    order: 2
    icon: LuArchiveRestore
    cameras:
      - GarageCam
      - KitchenCam
      - DoorbellCam
      - BackPorchCam
      #- BackyardCam
      - DrivewayCam
