import pygame
import sys

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
LIGHT_BLUE = (135, 206, 235)
RED = (255, 0, 0)
BROWN = (139, 69, 19)
YELLOW = (255, 255, 0)
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)

# Physics
GRAVITY = 0.8
JUMP_STRENGTH = -15
PLAYER_SPEED = 5

class Player:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.width = 40
        self.height = 60
        self.vel_x = 0
        self.vel_y = 0
        self.on_ground = False
        self.rect = pygame.Rect(x, y, self.width, self.height)
        
    def update(self, platforms):
        # Handle input
        keys = pygame.key.get_pressed()
        self.vel_x = 0
        
        if keys[pygame.K_LEFT]:
            self.vel_x = -PLAYER_SPEED
        if keys[pygame.K_RIGHT]:
            self.vel_x = PLAYER_SPEED
        if keys[pygame.K_UP] and self.on_ground:
            self.vel_y = JUMP_STRENGTH
            self.on_ground = False
            
        # Apply gravity
        self.vel_y += GRAVITY
        
        # Update position
        self.x += self.vel_x
        self.y += self.vel_y
        
        # Update rect
        self.rect.x = self.x
        self.rect.y = self.y
        
        # Check platform collisions
        self.on_ground = False
        for platform in platforms:
            if self.rect.colliderect(platform.rect):
                # Landing on top of platform
                if self.vel_y > 0 and self.rect.bottom <= platform.rect.top + 10:
                    self.y = platform.rect.top - self.height
                    self.vel_y = 0
                    self.on_ground = True
                # Hitting platform from below
                elif self.vel_y < 0 and self.rect.top >= platform.rect.bottom - 10:
                    self.y = platform.rect.bottom
                    self.vel_y = 0
                # Hitting platform from the side
                else:
                    if self.vel_x > 0:  # Moving right
                        self.x = platform.rect.left - self.width
                    elif self.vel_x < 0:  # Moving left
                        self.x = platform.rect.right
                        
        # Keep player on screen horizontally
        if self.x < 0:
            self.x = 0
        elif self.x > SCREEN_WIDTH - self.width:
            self.x = SCREEN_WIDTH - self.width
            
        # Reset if player falls off screen
        if self.y > SCREEN_HEIGHT:
            self.x = 50
            self.y = SCREEN_HEIGHT - 100
            self.vel_y = 0
            
        # Update rect after all position changes
        self.rect.x = self.x
        self.rect.y = self.y
        
    def draw(self, screen):
        # Draw player body (brown)
        pygame.draw.rect(screen, BROWN, (self.x, self.y + 10, self.width, self.height - 10))
        # Draw player hat (red)
        pygame.draw.rect(screen, RED, (self.x, self.y, self.width, 15))

class Platform:
    def __init__(self, x, y, width, height):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.rect = pygame.Rect(x, y, width, height)
        
    def draw(self, screen):
        pygame.draw.rect(screen, GREEN, self.rect)
        pygame.draw.rect(screen, BLACK, self.rect, 2)

class Coin:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.width = 20
        self.height = 20
        self.rect = pygame.Rect(x, y, self.width, self.height)
        self.collected = False
        
    def draw(self, screen):
        if not self.collected:
            pygame.draw.circle(screen, YELLOW, (self.x + 10, self.y + 10), 10)
            pygame.draw.circle(screen, BLACK, (self.x + 10, self.y + 10), 10, 2)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Mario-style Platformer")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        
        # Create player
        self.player = Player(50, SCREEN_HEIGHT - 100)
        
        # Create platforms
        self.platforms = [
            Platform(0, SCREEN_HEIGHT - 20, SCREEN_WIDTH, 20),  # Ground
            Platform(200, 450, 150, 20),  # Platform 1
            Platform(450, 350, 150, 20),  # Platform 2
            Platform(100, 250, 150, 20),  # Platform 3
        ]
        
        # Create coins
        self.coins = [
            Coin(250, 420),  # On platform 1
            Coin(500, 320),  # On platform 2
            Coin(150, 220),  # On platform 3
            Coin(600, 500),  # On ground
            Coin(350, 500),  # On ground
        ]
        
        self.score = 0
        
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
        return True
        
    def update(self):
        self.player.update(self.platforms)
        
        # Check coin collection
        for coin in self.coins:
            if not coin.collected and self.player.rect.colliderect(coin.rect):
                coin.collected = True
                self.score += 10
                
    def draw(self):
        # Clear screen with light blue background
        self.screen.fill(LIGHT_BLUE)
        
        # Draw platforms
        for platform in self.platforms:
            platform.draw(self.screen)
            
        # Draw coins
        for coin in self.coins:
            coin.draw(self.screen)
            
        # Draw player
        self.player.draw(self.screen)
        
        # Draw score
        score_text = self.font.render(f"Score: {self.score}", True, BLACK)
        self.screen.blit(score_text, (10, 10))
        
        # Draw instructions
        instructions = [
            "Arrow Keys: Move and Jump",
            "Collect coins for points!"
        ]
        for i, instruction in enumerate(instructions):
            text = pygame.font.Font(None, 24).render(instruction, True, BLACK)
            self.screen.blit(text, (10, 50 + i * 25))
        
        pygame.display.flip()
        
    def run(self):
        running = True
        while running:
            running = self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(FPS)
            
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
