#[include fluidd.cfg]

[exclude_object]

[include conf.d/*.conf]

[include macros.cfg]

[include stealthburner_leds.cfg]

[include klicky-probe.cfg]

[include autoz.cfg]

[include Adaptive_Mesh.cfg]

[include Line_Purge.cfg]

[include bedfans.cfg]

[include filament_runout.cfg]

[virtual_sdcard]
path: /home/<USER>/printer_data/gcodes

[pause_resume]

[display_status]

[skew_correction] 

[temperature_sensor raspberry_pi]
sensor_type: temperature_host
min_temp: 10
max_temp: 100

[temperature_sensor Octopus]
sensor_type: temperature_mcu
min_temp: 10
max_temp: 100

[temperature_sensor Canbus]
sensor_type: temperature_mcu
sensor_mcu: EBBCan
min_temp: 0
max_temp: 100
[virtual_sdcard]
path: /home/<USER>/printer_data/gcodes
on_error_gcode: CANCEL_PRINT

[mcu]

canbus_uuid: 83a413deaac0

[mcu EBBCan]

canbus_uuid: c4deba1e6d4d

[adxl345]
cs_pin: EBBCan:PA9
spi_bus = spi2
# spi_software_sclk_pin: sht36v2:PB13
# spi_software_mosi_pin: sht36v2:PB15
# spi_software_miso_pin: sht36v2:PB14
#--------------------------------------------------------------------
[resonance_tester]
accel_chip: adxl345
probe_points: 175,175,10

[printer]

kinematics: corexy
max_velocity: 300
max_accel: 10000             #Max 4000
max_accel_to_decel: 5000
max_z_velocity: 15          #Max 15 for 12V TMC Drivers, can increase for 24V
max_z_accel: 350
square_corner_velocity: 5.0

#####################################################################
#   X/Y Stepper Settings
#####################################################################

##  B Stepper - Left
##  Connected to MOTOR_0
##  Endstop connected to DIAG_0
[stepper_x]
step_pin: PF13
dir_pin: PF12
enable_pin: !PF14
rotation_distance: 40.06
microsteps: 32
full_steps_per_rotation:200  #set to 400 for 0.9 degree stepper
endstop_pin: ^EBBCan: PA1
position_min: 0
position_endstop: 350
position_max: 350

homing_speed: 75   #Max 100
homing_retract_dist: 5
second_homing_speed: 10
homing_positive_dir: true

[tmc2209 stepper_x]
uart_pin: PC4
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0

##  A Stepper - Right
##  Connected to MOTOR_1
##  Endstop connected to DIAG_1
[stepper_y]
step_pin: PG0
dir_pin: PG1
enable_pin: !PF15
rotation_distance: 39.95
microsteps: 32
full_steps_per_rotation:200  #set to 400 for 0.9 degree stepper
endstop_pin: PG12
position_min: 0
position_endstop: 350
position_max: 350

homing_speed: 75  #Max 100
homing_retract_dist: 5
second_homing_speed: 10
homing_positive_dir: true

[tmc2209 stepper_y]
uart_pin: PD11
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0
 
#####################################################################
#   Z Stepper Settings
#####################################################################

## Z0 Stepper - Front Left
##  Connected to MOTOR_2
##  Endstop connected to DIAG_2
[stepper_z]
step_pin: PF11
dir_pin: PG3
enable_pin: !PG5
rotation_distance: 40
gear_ratio: 80:16
microsteps: 32
endstop_pin: PG10
##  Z-position of nozzle (in mm) to z-endstop trigger point relative to print surface (Z0)
##  (+) value = endstop above Z0, (-) value = endstop below
##  Increasing position_endstop brings nozzle closer to the bed
##  After you run Z_ENDSTOP_CALIBRATE, position_endstop will be stored at the very end of your config
#position_endstop: -0.5
##--------------------------------------------------------------------

position_max: 320

##--------------------------------------------------------------------
position_min: -5
homing_speed: 8
second_homing_speed: 3
homing_retract_dist: 3

##  Make sure to update below for your relevant driver (2208 or 2209)
[tmc2209 stepper_z]
uart_pin: PC6
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0

##  Z1 Stepper - Rear Left
##  Connected to MOTOR_3
[stepper_z1]
step_pin: PG4
dir_pin: !PC1
enable_pin: !PA0
rotation_distance: 40
gear_ratio: 80:16
microsteps: 32

##  Make sure to update below for your relevant driver (2208 or 2209)
[tmc2209 stepper_z1]
uart_pin: PC7
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0

##  Z2 Stepper - Rear Right
##  Connected to MOTOR_4
[stepper_z2]
step_pin: PF9
dir_pin: PF10
enable_pin: !PG2
rotation_distance: 40
gear_ratio: 80:16
microsteps: 32

##  Make sure to update below for your relevant driver (2208 or 2209)
[tmc2209 stepper_z2]
uart_pin: PF2
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0

##  Z3 Stepper - Front Right
##  Connected to MOTOR_5
[stepper_z3]
step_pin: PC13
dir_pin: !PF0
enable_pin: !PF1
rotation_distance: 40
gear_ratio: 80:16
microsteps: 32

##  Make sure to update below for your relevant driver (2208 or 2209)
[tmc2209 stepper_z3]
uart_pin: PE4
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0


#####################################################################
#   Extruder
#####################################################################

##  Connected to MOTOR_6
##  Heater - HE0
##  Thermistor - T0
[extruder]
step_pin: EBBCan: PB4
dir_pin: EBBCan: PB3
enable_pin: !EBBCan: PA15
##  Update value below when you perform extruder calibration
##  If you ask for 100mm of filament, but in reality it is 98mm:
##  rotation_distance = <previous_rotation_distance> * <actual_extrude_distance> / 100
##  22.6789511 is a good starting point
#rotation_distance: 43.89   #Bondtech 5mm Drive Gears
rotation_distance: 22
gear_ratio: 50:10               #BMG Gear Ratio
microsteps: 32
full_steps_per_rotation: 200    #200 for 1.8 degree, 400 for 0.9 degree
nozzle_diameter: 0.400
filament_diameter: 1.75
heater_pin: EBBCan: PA8
## Check what thermistor type you have. See https://www.klipper3d.org/Config_Reference.html#common-thermistors for common thermistor types.
## Use "Generic 3950" for NTC 100k 3950 thermistors
sensor_type: PT1000
sensor_pin: EBBCan: PA3
pullup_resistor: 4700
min_temp: 10
max_temp: 275
max_power: 1.0
min_extrude_temp: 10
#control = pid
#pid_kp = 26.213
#pid_ki = 1.304
#pid_kd = 131.721
##  Try to keep pressure_advance below 1.0
pressure_advance: 0.03
##  Default is 0.040, leave stock
#pressure_advance_smooth_time: 0.040
max_extrude_only_distance: 400
#max_extrude_only_velocity: 220
max_extrude_cross_section: 12

##  E0 on MOTOR6
##  Make sure to update below for your relevant driver (2208 or 2209)
#[tmc2209 extruder]
#uart_pin: PE1
#interpolate: false
##run_current: 0.5
#run_current: 0.2
#sense_resistor: 0.110
#stealthchop_threshold: 0

[tmc2209 extruder]
uart_pin: EBBCan: PB5
diag_pin: EBBCan: PB6
interpolate: false
run_current: 0.8
sense_resistor: 0.110
stealthchop_threshold: 0


#####################################################################
#   Bed Heater
#####################################################################

##  SSR Pin - HE1
##  Thermistor - TB
[heater_bed]
##  Uncomment the following line if using the default SSR wiring from the docs site
heater_pin: PA3
##  Other wiring guides may use BED_OUT to control the SSR. Uncomment the following line for those cases
#heater_pin: PA1
## Check what thermistor type you have. See https://www.klipper3d.org/Config_Reference.html#common-thermistors for common thermistor types.
## Use "Generic 3950" for Keenovo heaters
sensor_type: Generic 3950
sensor_pin: PF3
##  Adjust Max Power so your heater doesn't warp your bed. Rule of thumb is 0.4 watts / cm^2 .
max_power: 0.6
min_temp: 0
max_temp: 120
#control: pid
#pid_kp: 58.437
#pid_ki: 2.347
#pid_kd: 363.769
pwm_cycle_time: 0.0166

[temperature_sensor chamber]
sensor_type: NTC 100K MGB18-104F39050L32
sensor_pin: PF4
min_temp: 0
max_temp: 100
gcode_id: C

#####################################################################
#   Probe
#####################################################################

##  Inductive Probe
##  This probe is not used for Z height, only Quad Gantry Leveling
[probe]

#--------------------------------------------------------------------

## Select the probe port by type:
## For the PROBE port. Will not work with Diode. May need pull-up resistor from signal to 24V.
#pin: ~!PB7
## For the DIAG_7 port. NEEDS BAT85 DIODE! Change to !PG15 if probe is NO.
pin: ^EBBCan: PC15
## For Octopus Pro PROBE port; NPN and PNP proximity switch types can be set by jumper
#pin: ~!PC5

#--------------------------------------------------------------------

x_offset: 0
y_offset: 19.75
z_offset: 5
speed: 10
samples: 1 #3
samples_result: median
sample_retract_dist: 1
samples_tolerance: 0.01
samples_tolerance_retries: 3

#####################################################################
#   Fan Control
#####################################################################

##  Print Cooling Fan - FAN0
[fan]
pin: EBBCan: PB11
kick_start_time: 0.5
max_power: 1.0
##  Depending on your fan, you may need to increase this value
##  if your fan will not start. Can change cycle_time (increase)
##  if your fan is not able to slow down effectively
off_below: 0.10

[controller_fan under1]
pin: PD14
max_power: 0.2
kick_start_time: 0.5
off_below: 0.10
heater: heater_bed

[controller_fan under2]
pin: PD15
max_power: 0.2
kick_start_time: 0.5
off_below: 0.10
heater: heater_bed

##  Hotend Fan - FAN1
[heater_fan hotend_fan]
pin: EBBCan: PB10
max_power: 1.0
kick_start_time: 0.5
heater: extruder
heater_temp: 50.0
##  If you are experiencing back flow, you can reduce fan_speed
#fan_speed: 1.0

##  Controller fan - FAN2
[controller_fan controller_fan]
pin: PD12
kick_start_time: 0.5
heater: heater_bed

##  Exhaust fan - FAN3
[heater_fan exhaust_fan]
pin: PD13
max_power: 1.0
shutdown_speed: 0.0
kick_start_time: 5.0
heater: heater_bed
heater_temp: 60
fan_speed: 0.2

##  Exhaust fan - FAN3
#[fan_generic top_fan]
#pin: PD13
#max_power: 1.0
#shutdown_speed: 0.0
#kick_start_time: 5.0
#enable_pin: PA8
#heater: heater_bed
#heater_temp: 108  #only turn on exhaust fan when printing abs, 220c and above
#fan_speed: 0.2

#[gcode_macro EXHAUST_FAN]
#gcode:
#    {% set HEATER_TARGET = params.HEATER_TARGET|default(60)|float %}
#    {% set FAN_TARGET = params.FAN_TARGET|default(100)|float %}
#    {% set FAN_SPEED = params.FAN_SPEED|default(1)|float %}
#    {% set FAN_NAME = params.FAN_NAME|default('top_fan')|string %}
#    
#    {% if HEATER_TARGET >= FAN_TARGET %}
#        SET_FAN_SPEED FAN={FAN_NAME} SPEED={FAN_SPEED} # Turn on bed_fan if target is above or equal.
#    {% else %}
#        SET_FAN_SPEED FAN={FAN_NAME} SPEED=0 # Turn off bed_fan if target is below bed_target
#    {% endif %}
# Assuming you have something like this in your START_PRINT macro:
# {% set BED_TEMP = params.BED_TEMP|default(60)|float %}
# 
# Then you can add the following line to use this macro:
# 
# TARGET_FAN HEATER_TARGET={BED_TEMP} FAN_TARGET=100
# 
# This will start the fan if the BED_TEMP target is greater or equal to 100.
# 
# 
# You can now optionally specify the following parameters:
# 
# FAN_SPEED: This is a value from 0-1 that represents the fan speed you want to use.
# 
# FAN_NAME: This should be the name of the generic_fan you wish to control.
# 
# This allows us to use multiple fan speeds and even multiple fans with this macro.
# 
# I also changed BED_TARGET to HEATER_TARGET to make this more generic for
# use with any heater/fan combination. Sorry, last breaking change to this macro, I promise!

#####################################################################
#   LED Control
#####################################################################

## Chamber Lighting - HE2 Connector (Optional)
[output_pin caselight]
pin: PB10
pwm:true
shutdown_value: 0
value:1
cycle_time: 0.01

#####################################################################
#   Homing and Gantry Adjustment Routines
#####################################################################

[idle_timeout]
timeout: 21600

#[safe_z_home]
##  XY Location of the Z Endstop Switch
##  Update -10,-10 to the XY coordinates of your endstop pin 
##  (such as 157,305) after going through Z Endstop Pin
##  Location Definition step.
#home_xy_position:234.5,348.4
#speed:100
#z_hop:10


##  Use QUAD_GANTRY_LEVEL to level a gantry.
##  Min & Max gantry corners - measure from nozzle at MIN (0,0) and 
##  MAX (250, 250), (300,300), or (350,350) depending on your printer size
##  to respective belt positions
[quad_gantry_level]
#--------------------------------------------------------------------
##  Gantry Corners
gantry_corners:
   -60,-10
   410,420
##  Probe points
points:
   50,25
   50,275
   300,275
   300,25

#--------------------------------------------------------------------
speed: 350
horizontal_move_z:7
retries: 5
retry_tolerance: 0.0075
max_adjust: 10

########################################
# EXP1 / EXP2 (display) pins
########################################

[bed_mesh]
speed: 350
horizontal_move_z: 7
##--------------------------------------------------------------------

mesh_min: 40, 40
mesh_max: 310,310
##--------------------------------------------------------------------
fade_start: 0.6
fade_end: 10.0
probe_count: 5
algorithm: lagrange #bicubic
zero_reference_position: 175,175
#relative_reference_index: 5 #12

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE8, EXP1_2=PE7,
    EXP1_3=PE9, EXP1_4=PE10,
    EXP1_5=PE12, EXP1_6=PE13,    # Slot in the socket on this side
    EXP1_7=PE14, EXP1_8=PE15,
    EXP1_9=<GND>, EXP1_10=<5V>,

    # EXP2 header
    EXP2_1=PA6, EXP2_2=PA5,
    EXP2_3=PB1, EXP2_4=PA4,
    EXP2_5=PB2, EXP2_6=PA7,      # Slot in the socket on this side
    EXP2_7=PC15, EXP2_8=<RST>,
    EXP2_9=<GND>, EXP2_10=<5V>

#####################################################################
#   Displays
#####################################################################

##  mini12864 LCD Display
[display]
lcd_type: uc1701
cs_pin: EXP1_3
a0_pin: EXP1_4
rst_pin: EXP1_5
encoder_pins: ^EXP2_5, ^EXP2_3
click_pin: ^!EXP1_2
contrast: 63
spi_software_miso_pin: EXP2_1
spi_software_mosi_pin: EXP2_6
spi_software_sclk_pin: EXP2_2

##  To control Neopixel RGB in mini12864 display
[neopixel btt_mini12864]
pin: EXP1_6
chain_count: 3
initial_RED: 0.0
initial_GREEN: 0.588
initial_BLUE: 1
color_order: RGB

##  Set RGB values on boot up for each Neopixel. 
##  Index 1 = display, Index 2 and 3 = Knob
[delayed_gcode setdisplayneopixel]
initial_duration: 1
gcode:
        SET_LED LED=btt_mini12864 RED=0 GREEN=0.588 BLUE=1 INDEX=1 TRANSMIT=0
        SET_LED LED=btt_mini12864 RED=0 GREEN=0.588 BLUE=1 INDEX=2 TRANSMIT=0
        SET_LED LED=btt_mini12864 RED=0 GREEN=0.588 BLUE=1 INDEX=3

#*# <---------------------- SAVE_CONFIG ---------------------->
#*# DO NOT EDIT THIS BLOCK OR BELOW. The contents are auto-generated.
#*#
#*# [probe]
#*#
#*# [stepper_z]
#*# position_endstop = 1.840
#*#
#*# [extruder]
#*# control = pid
#*# pid_kp = 21.564
#*# pid_ki = 2.146
#*# pid_kd = 54.180
#*#
#*# [heater_bed]
#*# control = pid
#*# pid_kp = 33.009
#*# pid_ki = 1.310
#*# pid_kd = 207.954
#*#
#*# [bed_mesh default]
#*# version = 1
#*# points =
#*# 	0.051249, 0.071249, 0.044999, 0.062499, 0.056249
#*# 	0.004999, 0.011249, 0.011249, -0.005001, 0.002499
#*# 	-0.008751, 0.019999, -0.000001, -0.008751, -0.008751
#*# 	0.046249, 0.053749, 0.041249, 0.027499, 0.044999
#*# 	0.076249, 0.101249, 0.083749, 0.066249, 0.074999
#*# tension = 0.2
#*# min_x = 58.31
#*# algo = lagrange
#*# y_count = 5
#*# mesh_y_pps = 2
#*# min_y = 58.31
#*# x_count = 5
#*# max_y = 291.68
#*# mesh_x_pps = 2
#*# max_x = 291.68
#*#
#*# [input_shaper]
#*# shaper_type_x = mzv
#*# shaper_freq_x = 57.0
#*# shaper_type_y = 2hump_ei
#*# shaper_freq_y = 47.2
#*#
#*# [skew_correction CaliFlower]
#*# xy_skew = 0.007517985709455608
#*# xz_skew = 0.0
#*# yz_skew = 0.0
