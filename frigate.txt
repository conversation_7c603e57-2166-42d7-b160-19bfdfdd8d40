mqtt:
  host: ************
  user: mqtt-user
  password: System64!

database:
  path: /config/frigate.db

detectors:
  coral:
    type: edgetpu
    device: usb
    #model:
    #path: "/custom_model.tflite"

#ffmpeg:
#  hwaccel_args: preset-nvidia-h264

model:
  path: plus://8205bcbe8038ce6dfb0d598d6d3fa62e

cameras:
  FrontYard:
    ffmpeg:
      inputs:
        - path: rtsp://admin:starburst32@************:554/rtsp
          roles:
            - detect
          #  - clips
          #  - record
          #- rtmp
    detect:
      width: 3840
      height: 2160
      fps: 15

    motion:
      mask:
        - 620,0,1926,0,1955,263,1015,418,723,488,684,571,372,658,0,741,0,209,0,0
    objects:
      filters:
        car:
          mask:
            - 615,0,837,0
  Garage:
    ffmpeg:
      inputs:
        - path: rtsp://admin:starburst32@*************:554/rtsp
          #roles:
          #  - detect
          #  - clips
          #  - record
          #- rtmp
    detect:
      width: 3840
      height: 2160
      fps: 15
    motion:
      mask:
        - 429,666,834,545,904,813,1059,788,1048,483,1456,328,1554,723,1931,684,2172,219,2987,627,3589,718,3599,0,0,0,21,759,83,700,145,731,269,646
  OwenCam:
    ffmpeg:
      inputs:
        - path: rtsp://***********:8554/1080p?mp4
    detect:
      width: 1920
      height: 1080
      fps: 15
  EianCam:
    ffmpeg:
      inputs:
        - path: rtsp://brianrich:starburst@*************/live
    detect:
      width: 1920
      height: 1080
      fps: 15
objects:
  track:
    - person
    - car
  #- bird
  #- airplane
    - bicycle
  #- motorcycle
    - bus
  #- cat
  #- dog
    - license_plate
    - amazon
    - fedex
    - ups
    - package

snapshots:
  # Optional: Enable writing jpg snapshot to /media/frigate/clips (default: shown below)
  enabled: false
  # Optional: save a clean PNG copy of the snapshot image (default: shown below)
  clean_copy: true
  # Optional: print a timestamp on the snapshots (default: shown below)
  timestamp: false
  # Optional: draw bounding box on the snapshots (default: shown below)
  bounding_box: true
  # Optional: crop the snapshot (default: shown below)
  crop: false
  # Optional: height to resize the snapshot to (default: original size)
  #height: 175
  # Optional: Restrict snapshots to objects that entered any of the listed zones (default: no required zones)
  #required_zones: []
  # Optional: Camera override for retention settings (default: global values)
  retain:
    # Required: Default retention days (default: shown below)
    default: 1
    objects:
      person: 1
      car: 1
      package: 1
      fedex: 1
      ups: 1
      amazon: 1
version: 0.14
camera_groups:
  Kids:
    order: 1
    icon: LuPersonStanding
    cameras:
      - EianCam
      - OwenCam
  Outside:
    order: 2
    icon: LuArchiveRestore
    cameras:
      - FrontYard
      - Garage
