# Mario-Style Platformer Game

A simple 2D side-scrolling platformer game built with Python and Pygame, inspired by classic Mario games.

## Features

- **Player Character**: A character with a red hat that can move and jump
- **Controls**:
  - Left/Right arrow keys: Move horizontally
  - Up arrow key: Jump (can double jump while in air)
- **Game World**: 
  - Light blue background
  - Three platforms at different heights for jumping gameplay
  - Ground platform to prevent falling off the world
- **Collectibles**: Gold coins scattered throughout the level
- **Physics**: Gravity system with collision detection
- **Scoring**: Collect coins to increase your score

## Requirements

- Python 3.x
- Pygame library

## Installation

1. Make sure you have Python installed
2. Install Pygame:
   ```bash
   pip install pygame
   ```

## How to Run

```bash
python platformer_game.py
```

## How to Play

1. Use the **Left** and **Right** arrow keys to move your character
2. Press the **Up** arrow key to jump
3. Press the **Up** arrow key again while in the air for a double jump
4. Collect yellow coins to increase your score
5. Jump between platforms to reach higher areas
6. If you fall off the bottom of the screen, you'll respawn at the starting position

## Game Elements

- **Player**: Brown character with a red hat
- **Platforms**: Green rectangular platforms you can jump on
- **Coins**: Yellow circular coins to collect
- **Score**: Displayed in the top-left corner

## Controls Summary

- ⬅️ **Left Arrow**: Move left
- ➡️ **Right Arrow**: Move right
- ⬆️ **Up Arrow**: Jump (press again in air for double jump)
- ❌ **Close Window**: Quit game

Enjoy playing your Mario-style platformer!
