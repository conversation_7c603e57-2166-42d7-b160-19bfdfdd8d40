mqtt:
  enabled: True
  host: ************
  user: mqtt-user
  password: System64!

database:
  path: /config/frigate.db

detectors:
  coral:
    type: edgetpu
    device: usb

ffmpeg:
  hwaccel_args: preset-nvidia-h264

model:
  path: plus://8205bcbe8038ce6dfb0d598d6d3fa62e

cameras:
  FrontYard:
    ffmpeg:
      inputs:
        - path: rtsp://admin:starburst32@************:554/rtsp
          roles:
            - detect
            - clips
            - record
            - rtmp
    detect:
      enabled: True
      width: 3840
      height: 2160
      fps: 10
    record:
      enabled: True
      retain:
        days: 3
        mode: all
      events:
        retain:
          default: 30
          mode: motion
    motion:
      mask: 
        0.161,0,1,0,1,0.178,0.793,0.106,0.554,0.102,0.392,0.133,0.234,0.188,0.11,0.257,0,0.343,0,0.097,0,0
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
    objects:
      filters:
        car:
          mask: 
            0.161,0,1,0,1,0.178,0.793,0.106,0.554,0.102,0.392,0.133,0.234,0.188,0.11,0.257,0,0.343,0,0.097,0,0
    review:
      detections:
        labels: []
  Garage:
    ffmpeg:
      inputs:
        - path: rtsp://admin:starburst32@*************:554/rtsp
          roles:
            - detect
            - clips
            - record
            - rtmp
    detect:
      width: 3840
      height: 2160
      fps: 10
    record:
      enabled: True
      retain:
        days: 3
        mode: all
      events:
        retain:
          default: 30
          mode: motion
    motion:
      threshold: 60
      contour_area: 10
      improve_contrast: 'true'
      mask: 0.315,0.157,0.388,0.114,0.491,0.071,0.569,0.072,0.647,0.104,0.651,-0.001,0.315,0
    objects:
      filters:
        car:
          mask: 0.766,0.311,0.963,0.336,0.961,0.452,0.765,0.419
    review:
      detections:
        labels: []
  #EianCam:
  #  ffmpeg:
  #    inputs:
  #      - path: rtsp://brianrich:starburst@*************/live
  #  detect:
  #    width: 1920
  #    height: 1080
  #    fps: 10
  #  record:
  #    enabled: True
  #    retain:
  #      days: 3
  #      mode: all
  #    events:
  #      retain:
  #        default: 30
  #        mode: motion
  #  motion:
  #    threshold: 55
  #    contour_area: 10
  #    improve_contrast: 'true'
  #    mask: 1,0,1,1,0.454,1,0.733,0.785,0.615,0.382,0.193,0.245,0.296,1,0,1,0,0
  #  review:
  #    detections:
  #      labels: []
objects:
  track:
    - person
    - car
  #- bird
  #- airplane
    - bicycle
  #- motorcycle
    - bus
  #- cat
  #- dog
    - license_plate
    - amazon
    - fedex
    - ups
    - package

snapshots:
  # Optional: Enable writing jpg snapshot to /media/frigate/clips (default: shown below)
  enabled: true
  # Optional: save a clean PNG copy of the snapshot image (default: shown below)
  clean_copy: true
  # Optional: print a timestamp on the snapshots (default: shown below)
  timestamp: false
  # Optional: draw bounding box on the snapshots (default: shown below)
  bounding_box: true
  # Optional: crop the snapshot (default: shown below)
  crop: false
  # Optional: height to resize the snapshot to (default: original size)
  #height: 175
  # Optional: Restrict snapshots to objects that entered any of the listed zones (default: no required zones)
  #required_zones: []
  # Optional: Camera override for retention settings (default: global values)
  retain:
    # Required: Default retention days (default: shown below)
    default: 1
    objects:
      person: 1
      car: 1
      package: 1
      fedex: 1
      ups: 1
      amazon: 1

version: 0.14
camera_groups:
  Kids:
    order: 1
    icon: LuPersonStanding
    cameras:
      - EianCam
      - OwenCam
  Outside:
    order: 2
    icon: LuArchiveRestore
    cameras:
      - FrontYard
      - Garage
